<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Creative Mind - Precise Wave</title>
    <style>
        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
            background-color: #e6e7e9;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            box-sizing: border-box;
        }

        .poster-container {
            display: flex;
            width: 800px;
            height: 500px;
            background-color: #ffffff;
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.12);
            border-radius: 12px;
            overflow: hidden;
        }

        .wave-panel {
            width: 42%;
            height: 100%;
            background-color: #1d3557;
            color: white;
            padding: 30px 25px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            cursor: pointer;
            transition: transform 0.2s ease-out;
            border-top-left-radius: 12px;
            border-bottom-left-radius: 12px;
            border-top-right-radius: 70px 50%;
            border-bottom-right-radius: 70px 50%;
            box-shadow: 3px 0px 8px rgba(0, 0, 0, 0.1);
            z-index: 1;
        }

        .wave-panel:hover {
            transform: translateX(-2px);
        }

        .wave-panel .logo-area,
        .wave-panel .more-button-area {
            font-size: 0.8em;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.7px;
            background-color: #457b9d;
            padding: 9px 20px;
            border-radius: 50px;
            color: #f1faee;
            transition: background-color 0.2s ease;
            z-index: 2;
        }

        .wave-panel .logo-area {
            margin-bottom: auto;
        }
        .wave-panel .more-button-area {
            margin-top: auto;
        }

        .wave-panel .more-button-area:hover,
        .wave-panel .logo-area:hover {
            background-color: #5a94bb;
        }

        .wave-panel-main-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            text-align: left;
            width: 100%;
            padding: 20px 0;
            z-index: 2;
        }

        #mainText {
            font-size: 2.9em;
            color: #ffffff;
            margin-bottom: 15px;
            font-weight: 700;
            letter-spacing: 0.2px;
            line-height: 1.1;
            text-transform: uppercase;
            transition: color 0.3s ease, transform 0.3s ease;
        }

        #subText {
            font-size: 0.82em;
            color: #a8dadc;
            max-width: 95%;
            line-height: 1.6;
            margin-bottom: 25px;
            transition: color 0.3s ease;
            font-weight: 400;
        }

        #mainText.active-text {
            color: #bde0fe;
            transform: scale(1.01);
        }


        .content-area {
            width: 58%;
            height: 100%;
            padding: 30px 35px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .content-area .top-nav {
            align-self: flex-end;
            display: flex;
            gap: 20px;
        }

        .content-area .top-nav a {
            text-decoration: none;
            color: #6c757d;
            font-size: 0.9em;
            font-weight: 500;
            transition: color 0.2s ease;
        }

        .content-area .top-nav a:hover {
            color: #343a40;
        }

        .head-silhouette-placeholder {
            flex-grow: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            font-style: italic;
            color: #adb5bd;
            font-size: 0.9em;
            text-align: center;
        }

    </style>
</head>
<body>

    <div class="poster-container">
        <div class="wave-panel" id="wavePanel">
            <div class="logo-area">LOGO</div>

            <div class="wave-panel-main-content">
                <h1 id="mainText">CREATIVE<br>MIND</h1>
                <p id="subText">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                </p>
            </div>

            <div class="more-button-area">MORE</div>
        </div>

        <div class="content-area">
            <nav class="top-nav">
                <a href="#">Home</a>
                <a href="#">About</a>
                <a href="#">Portfolio</a>
                <a href="#">Contact</a>
                <a href="#">FAQ</a>
            </nav>
            <div class="head-silhouette-placeholder">
                (Right content area)
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const wavePanel = document.getElementById('wavePanel');
            const mainTextElement = document.getElementById('mainText');
            const subTextElement = document.getElementById('subText');

            const originalMainText = "CREATIVE<br>MIND";
            const originalSubText = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.";

            const hoverMainText = "IDEAS<br>IGNITED!";
            const hoverSubText = "Discover new possibilities and innovative solutions when you explore our creative services further.";

            if (wavePanel && mainTextElement && subTextElement) {
                wavePanel.addEventListener('mouseover', function() {
                    mainTextElement.innerHTML = hoverMainText;
                    subTextElement.textContent = hoverSubText;
                    mainTextElement.classList.add('active-text');
                });

                wavePanel.addEventListener('mouseout', function() {
                    mainTextElement.innerHTML = originalMainText;
                    subTextElement.textContent = originalSubText;
                    mainTextElement.classList.remove('active-text');
                });

                const moreButton = wavePanel.querySelector('.more-button-area');
                if (moreButton) {
                    moreButton.addEventListener('click', function(event) {
                        event.stopPropagation();
                        console.log("More button clicked!");
                    });
                }
            } else {
                console.error("Required elements (wavePanel, mainText, or subText) not found!");
            }
        });
    </script>
</body>
</html>