<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no" />
    <title>Dynamic Family Tree - Precise Lines</title>
    <style type="text/css">
        @import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Lato:wght@400;700&display=swap');
        * { padding: 0; margin: 0; box-sizing: border-box; }
        body { min-height: 100vh; font-family: 'Lato', sans-serif; background-color: #fdfcf7; color: #000000; display: flex; flex-direction: column; align-items: center; padding: 30px 20px; overflow-x: auto; }
        .tree-title { font-family: 'Merriweather', serif; font-size: 36px; color: #000000; margin-bottom: 40px; font-weight: 700; }
        .tree { width: auto; min-width: max-content; text-align: center; user-select: none; }
        
        .tree ul { padding-top: 20px; position: relative; display: flex; justify-content: center; flex-wrap: nowrap; }
        .tree li { list-style-type: none; position: relative; padding: 0 10px; display: flex; flex-direction: column; align-items: center; flex-shrink: 0; }
        
        /* --- Connectors from PARENT UL to CHILD LI (person-node OR couple-unit) --- */
        .tree ul > li { padding-top: 20px; }
        .tree ul > li::before, 
        .tree ul > li::after { 
            content: ''; position: absolute; top: 0; right: 50%; border-top: 2px solid #000000; width: 50%; height: 20px; 
        }
        .tree ul > li::after { 
            right: auto; left: 50%; border-left: 2px solid #000000; 
        }
        .tree ul > li:only-child::before, 
        .tree ul > li:only-child::after { display: none; }
        .tree ul > li:first-child::before, 
        .tree ul > li:last-child::after { border-top: 0 none; } 
        .tree ul > li:last-child::before { border-right: 2px solid #000000; border-radius: 0 5px 0 0; }
        .tree ul > li:first-child::after { border-radius: 5px 0 0 0; }
        .tree > ul > li:only-child { padding-top: 0; }
        .tree ul li:only-child { padding-top: 0; }

        /* --- Couple Unit Structure & Lines --- */
        .tree li.couple-unit { padding-top: 20px; /* Space for its connector from its parent UL */ }
        .spouse-pair-container { 
            display: flex; justify-content: center; position: relative;
            padding-top: 20px; /* Space FOR marriage line to draw above nodes */
            margin-bottom: 0; 
        }
        .spouse-pair-container::before { /* The Marriage Line */
            content: ''; position: absolute; top: 10px; 
            left: calc(50% - ( (80px + 2*15px)/2 - 15px + 1px) ); /* (half of first node's wrapper) - (half of marriage line thickness) */
            right: calc(50% - ( (80px + 2*15px)/2 - 15px + 1px) );
            height: 2px; background-color: #000000;
        }
        .person-node-wrapper { 
            padding: 0 15px; /* Horizontal spacing BETWEEN spouses */
            position: relative; 
            display: flex; flex-direction: column; align-items: center;
        }
        .spouse-pair-container .person-node-wrapper::before { /* Vertical ticks from marriage line to each spouse node */
            content: ''; position: absolute; top: -10px; 
            left: 50%; transform: translateX(-50%); 
            width: 2px; height: 10px; background-color: #000000;
        }

        /* The "Add Child" button for a couple unit */
        .tree li.couple-unit > .add-btn {
            position: relative; 
            margin-top: 5px; /* Space between spouse-pair and this button */
            z-index: 1;
        }
        /* Line dropping from couple's add button DOWN to children's UL connector */
        .tree li.couple-unit.has-children > .add-btn::after { 
            content: ''; position: absolute; top: 100%; 
            left: 50%; transform: translateX(-50%);
            width: 2px; height: 20px; background-color: #000;
        }

        /* Children UL hanging from a parent */
        .tree li > ul { margin-top:0; padding-top: 20px; position: relative; }
        /* Vertical line from children's UL UP to meet parent's drop */
        .tree li > ul::before { 
            content: ''; position: absolute; top: 0px; left: 50%;
            border-left: 2px solid #000000; width: 0; height: 20px; 
            transform: translateX(-50%);
        }
         /* If children are under a couple that has the line from button, hide this default UL connector */
        .tree li.couple-unit.has-children > ul::before {
            display: none; 
        }
        

        .node-content { display: flex; flex-direction: column; align-items: center; margin-bottom: 5px; }
        .node-content img { width: 80px; height: 80px; border-radius: 50%; object-fit: cover; background-color: #ccc; margin-bottom: 8px; z-index: 1; border: 4px solid transparent; }
        .node-content .details { color: #000000; padding: 2px 5px; min-width: 100px; max-width: 160px; text-align: center; font-size: 14px; line-height: 1.4; }
        .node-content .details .name { font-weight: bold; font-size: 1em; word-break: break-word; }
        .node-content .details .role { font-size: 0.85em; color: #555; text-transform: capitalize; margin-top: 1px; }
        .add-btn { background-color: #5cb85c; color: white; border: none; width: 28px; height: 28px; border-radius: 50%; font-size: 18px; font-weight: bold; line-height: 26px; cursor: pointer; margin-top: 10px; box-shadow: 0 1px 3px rgba(0,0,0,0.2); transition: background-color 0.3s; flex-shrink: 0; }
        .add-btn:hover { background-color: #4cae4c; }
        .node-content:hover img { transform: scale(1.05); box-shadow: 0 0 10px rgba(0,0,0,0.2); }
        .node-color-1 { background-color: #ff7e67; } .node-color-2 { background-color: #ffda77; } .node-color-3 { background-color: #a0eade; } .node-color-4 { background-color: #b7e4c7; } .node-color-5 { background-color: #8ecae6; } .node-color-6 { background-color: #f7c5cc; }
    </style>
</head>
<body>

<div class="tree-title">The Family Tree</div>

<div class="tree">
    <ul>
        <li class="person-node" data-person-id="person-1">
            <div class="person-node-wrapper">
                <div class="node-content">
                    <img src="images/1.jpg" alt="Root Person"/> 
                    <div class="details">
                        <div class="name">You</div>
                        <div class="role">Me</div>
                    </div>
                </div>
                <button class="add-btn" onclick="handleAddClick(this)">+</button>
            </div>
        </li>
    </ul>
</div>

<script type="text/javascript">
    let personIdCounter = 1;
    const nodeColors = ["node-color-1", "node-color-2", "node-color-3", "node-color-4", "node-color-5", "node-color-6"];
    let colorIndex = 0;

    function getNextColorClass() {
        const colorClass = nodeColors[colorIndex % nodeColors.length];
        colorIndex++;
        return colorClass;
    }

    function getImageDataURL(successCallback, cancelOrFailCallback) {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = 'image/*';
        fileInput.style.display = 'none';
        let fileProcessed = false;
        fileInput.onchange = function(event) {
            fileProcessed = true;
            const file = event.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) { successCallback(e.target.result); }
                reader.onerror = function() { console.error("Error reading file."); cancelOrFailCallback(); }
                reader.readAsDataURL(file);
            } else { cancelOrFailCallback(); }
            if (document.body.contains(fileInput)) document.body.removeChild(fileInput);
            window.removeEventListener('focus', handleFocusAfterDialog); 
        };
        function handleFocusAfterDialog() {
            setTimeout(() => {
                if (!fileProcessed && document.body.contains(fileInput)) {
                    cancelOrFailCallback();
                    if (document.body.contains(fileInput)) document.body.removeChild(fileInput);
                }
            }, 300); 
        }
        window.addEventListener('focus', handleFocusAfterDialog, { once: true });
        document.body.appendChild(fileInput);
        fileInput.click();
    }

    function createPersonNodeWrapperDOM(nameInput, surnameInput, roleInput, imageUrl, forSpouseInCouple, coupleUnitLiContext) {
        const colorClass = getNextColorClass();
        const personNodeWrapper = document.createElement('div');
        personNodeWrapper.className = 'person-node-wrapper';

        const nodeContentDiv = document.createElement('div');
        nodeContentDiv.className = 'node-content';
        const newImg = document.createElement('img');
        newImg.src = imageUrl || 'images/default.png';
        newImg.alt = `${nameInput} ${surnameInput}`;
        newImg.classList.add(colorClass);
        newImg.onerror = function() { this.src = 'images/default.png'; this.classList.add(getNextColorClass());};
        
        const detailsDiv = document.createElement('div');
        detailsDiv.className = 'details';
        const nameDiv = document.createElement('div');
        nameDiv.className = 'name';
        nameDiv.textContent = `${nameInput} ${surnameInput}`.trim();
        const roleDiv = document.createElement('div');
        roleDiv.className = 'role';
        roleDiv.textContent = roleInput;
        detailsDiv.appendChild(nameDiv);
        detailsDiv.appendChild(roleDiv);
        nodeContentDiv.appendChild(newImg);
        nodeContentDiv.appendChild(detailsDiv);
        personNodeWrapper.appendChild(nodeContentDiv);
        
        const addButton = document.createElement('button');
        addButton.className = 'add-btn';
        addButton.textContent = '+';
        
        if (forSpouseInCouple && coupleUnitLiContext) {
             addButton.onclick = function() { this.disabled = true; addChild(coupleUnitLiContext, this); };
        } else {
             addButton.onclick = function() { handleAddClick(this); };
        }
        personNodeWrapper.appendChild(addButton);
        return personNodeWrapper;
    }
    
    function createChildLiNode(nameInput, surnameInput, roleInput, imageUrl) {
        const newLi = document.createElement('li');
        newLi.classList.add('person-node');
        newLi.dataset.personId = `person-${++personIdCounter}`;
        const personNodeWrapper = createPersonNodeWrapperDOM(nameInput, surnameInput, roleInput, imageUrl, false, null);
        newLi.appendChild(personNodeWrapper);
        return newLi;
    }

    function handleAddClick(buttonElement) {
        const mainLi = buttonElement.closest('li.person-node, li.couple-unit');
        if (!mainLi) { console.error("Could not find mainLi for button", buttonElement); return; }
        
        buttonElement.disabled = true; 

        if (mainLi.classList.contains('couple-unit')) {
            addChild(mainLi, buttonElement);
        } else if (mainLi.classList.contains('person-node')) {
            if (!mainLi.dataset.isCoupleMember) { 
                addSpouse(mainLi, buttonElement);
            } else { 
                 const coupleUnit = mainLi.closest('.couple-unit');
                 if (coupleUnit) addChild(coupleUnit, buttonElement);
                 else { buttonElement.disabled = false; }
            }
        } else {
            buttonElement.disabled = false; 
        }
    }

    function addSpouse(originalPersonLi, originalButtonElement) {
        const originalPersonWrapper = originalPersonLi.querySelector('.person-node-wrapper');
        if (!originalPersonWrapper) { originalButtonElement.disabled = false; return; }

        const name = prompt("Enter spouse's Name:", "Spouse Name");
        if (name === null) { originalButtonElement.disabled = false; return; }
        const surname = prompt("Enter spouse's Surname:", "Surname");
        if (surname === null) { originalButtonElement.disabled = false; return; }
        const role = prompt("Enter spouse's Role (e.g., Husband, Wife):", "Spouse");
        if (role === null) { originalButtonElement.disabled = false; return; }
        const finalName = name.trim() || "Spouse";
        const finalSurname = surname.trim();
        const finalRole = role.trim() || "Spouse Role";

        getImageDataURL(
            function(imageDataUrl) {
                const coupleUnitLi = document.createElement('li');
                coupleUnitLi.classList.add('couple-unit');
                coupleUnitLi.dataset.personId = `couple-${originalPersonLi.dataset.personId.split('-')[1]}`;

                const spousePairContainer = document.createElement('div');
                spousePairContainer.className = 'spouse-pair-container';
                
                originalPersonLi.dataset.isCoupleMember = "true";
                const originalPersonsOwnAddButton = originalPersonWrapper.querySelector('.add-btn');
                if(originalPersonsOwnAddButton) originalPersonsOwnAddButton.remove();

                const spouseNodeWrapper = createPersonNodeWrapperDOM(finalName, finalSurname, finalRole, imageDataUrl, true, coupleUnitLi);
                
                spousePairContainer.appendChild(originalPersonWrapper); 
                spousePairContainer.appendChild(spouseNodeWrapper);
                coupleUnitLi.appendChild(spousePairContainer);

                const addChildToCoupleButton = document.createElement('button');
                addChildToCoupleButton.className = 'add-btn';
                addChildToCoupleButton.textContent = '+';
                addChildToCoupleButton.style.marginTop = '0px'; // Position directly below spouse-pair
                addChildToCoupleButton.onclick = function() { this.disabled = true; addChild(coupleUnitLi, this); };
                coupleUnitLi.appendChild(addChildToCoupleButton);

                const existingChildrenUl = originalPersonLi.querySelector('ul');
                if (existingChildrenUl) {
                    coupleUnitLi.appendChild(existingChildrenUl);
                }
                
                originalPersonLi.parentNode.replaceChild(coupleUnitLi, originalPersonLi);
            }, 
            function() { 
                if (originalButtonElement && !originalButtonElement.closest('body')) {
                    // If original button was removed, we can't re-enable it directly.
                    // This branch might need more robust handling if prompts are cancelled after DOM change.
                } else if (originalButtonElement) {
                    originalButtonElement.disabled = false; 
                }
            }
        );
    }

    function addChild(targetLi, buttonElementToReEnable) {
        const name = prompt("Enter child's Name:", "Child Name");
        if (name === null) { if (buttonElementToReEnable) buttonElementToReEnable.disabled = false; return; }
        let defaultSurname = "";
        const primaryParentNameElement = targetLi.querySelector('.person-node-wrapper:first-child .details .name');
        if (primaryParentNameElement) {
            const parentFullName = primaryParentNameElement.textContent.trim();
            const parentFullNameParts = parentFullName.split(' ');
            if (parentFullNameParts.length > 0) {
                 defaultSurname = parentFullNameParts[parentFullNameParts.length -1];
            }
        }
        const surname = prompt("Enter child's Surname:", defaultSurname || "ChildSurname");
        if (surname === null) { if (buttonElementToReEnable) buttonElementToReEnable.disabled = false; return; }
        const role = prompt("Enter child's Role (e.g., Son, Daughter):", "Child");
        if (role === null) { if (buttonElementToReEnable) buttonElementToReEnable.disabled = false; return; }
        const finalName = name.trim() || "Child";
        const finalSurname = surname.trim();
        const finalRole = role.trim() || "Child Role";

        getImageDataURL(
            function(imageDataUrl) {
                const childLi = createChildLiNode(finalName, finalSurname, finalRole, imageDataUrl); 
                let childrenUl = targetLi.querySelector('ul');
                if (!childrenUl) {
                    childrenUl = document.createElement('ul');
                    targetLi.appendChild(childrenUl);
                }
                childrenUl.appendChild(childLi);
                targetLi.classList.add('has-children');
                if (buttonElementToReEnable) buttonElementToReEnable.disabled = false;
            },
            function() {
                if (buttonElementToReEnable) buttonElementToReEnable.disabled = false;
            }
        );
    }

    document.addEventListener('DOMContentLoaded', () => {
        const rootLi = document.querySelector('.tree > ul > li.person-node');
        const rootNodeWrapper = rootLi.querySelector('.person-node-wrapper');
        const rootNodeNameDiv = rootNodeWrapper.querySelector('.node-content .details .name');
        const rootNodeRoleDiv = rootNodeWrapper.querySelector('.node-content .details .role');
        const rootNodeImg = rootNodeWrapper.querySelector('.node-content img');
        const treeTitleEl = document.querySelector('.tree-title');

        const rootName = prompt("Enter Root Person's Name:", "Lodge") || "You";
        const rootSurname = prompt("Enter Root Person's Surname:", "Family") || "";
        const rootRole = prompt("Enter Root Person's Role:", "Me") || "Me";
        
        rootNodeNameDiv.textContent = `${rootName} ${rootSurname}`.trim();
        rootNodeRoleDiv.textContent = rootRole;
        rootNodeImg.alt = `${rootName} ${rootSurname}`.trim();
        let titleText = `The ${rootName} ${rootSurname} Family`;
        if (rootSurname.toLowerCase() === "family") {
            titleText = `The ${rootName} Family`;
        }
        treeTitleEl.textContent = titleText.replace('  ', ' ').trim();
        document.title = `${rootName}'s Family Tree`;

        const rootColorClass = getNextColorClass();
        rootNodeImg.classList.add(rootColorClass);
        rootLi.dataset.nodeColor = 1; 

        const changeRootImage = confirm("Set image for Root Person?");
        if (changeRootImage) {
            getImageDataURL(
                function(imageDataUrl) { rootNodeImg.src = imageDataUrl; },
                function() { rootNodeImg.src = 'images/1.jpg'; } 
            );
        } else {
             rootNodeImg.src = 'images/1.jpg';
        }
    });
</script>

</body>
</html>