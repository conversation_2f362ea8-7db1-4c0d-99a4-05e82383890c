<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .app{
            background-image: url(https://bakibra.dev/wp-content/uploads/2024/03/BB_Pattern_white.png);
            width: 90%;
            max-width: 600px;
            margin: 100px auto 0;
            border-radius: 10px;
            padding: 30px;
        }
        .h1Q{
            font-size: 25px;
            color:#0C0D0D;
            font-weight: 600;
            border-bottom: 1px solid #333;
            padding-bottom: 30px;
            text-align: center;
        }
        .quiz{
            padding: 20px 0;
        }
        .quiz .h2Q{
            font-size: 20px;
            color: #0C0D0D;
            font-weight: 600;
        }
        .btnQ{
            background: #F5923F;
            color: #FFFFFF;
            font-weight: 500;
            width: 100%;
            border: 1px solid #0C0D0D;
            padding: 10px;
            margin: 10px 0;
            text-align: left;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btnQ:hover:not([disabled]){
            background: #2A1148;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 500;
        }
        .btnQ:disabled{
            cursor: no-drop;
        }
        #next-btn{
            background: #F5923F;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 500;
            width: 150px;
            border: 1px solid #0C0D0D;
            padding: 10px;
            margin: 20px auto 0;
            border-radius: 4px;
            cursor: pointer;
            display: none;
        }
        .correct{
            background: #9aeabc;
            color: #0C0D0D;
        }
        .incorrect{
            background: #ff9393;
            color: #0C0D0D;
        }
    </style>
</head>
<body>
    <div class="app">
        <h1 class="h1Q">Challenging Bible Test</h1>
        <div class="quiz">
            <h2   class="h2Q"id="question">Question goes here</h2>
            <div id="answer-buttons">
                <button class="btnQ">Answer 1</button>
                <button class="btnQ">Answer 2</button>
                <button class="btnQ">Answer 3</button>
                <button class="btnQ">Answer 4</button>
            </div>
            <button id="next-btn">Next</button>
        </div>
    </div>
    <script>
        const questions = [
            {
                question: "Who was the priest-king of Salem who blessed Abram (Abraham) after his victory over Chedorlaomer and his allies?",
                answers: [
                    { text: "Aaron", correct: false},
                    { text: "Melchizedek", correct: true},
                    { text: "Eli", correct: false},
                    { text: "Zadok", correct: false}
                ]
            },
            {
                question: "Which prophet was commanded by God to marry Gomer, a prostitute, as a prophetic sign of Israel's unfaithfulness?",
                answers: [
                    { text: "Jeremiah", correct: false},
                    { text: "Ezekiel", correct: false},
                    { text: "Hosea", correct: true},
                    { text: "Amos", correct: false}
                ]
            },
            {
                question: "In the book of Judges, which judge made a rash vow to God that tragically resulted in the sacrifice of his own daughter?",
                answers: [
                    { text: "Samson", correct: false},
                    { text: "Gideon", correct: false},
                    { text: "Ehud", correct: false},
                    { text: "Jephthah", correct: true}
                ]
            },
            {
                question: "What was the name of the chief craftsman, filled with the Spirit of God, who was appointed to oversee the construction of the Tabernacle and its furnishings?",
                answers: [
                    { text: "Aholiab", correct: false},
                    { text: "Bezalel", correct: true},
                    { text: "Huram-abi (Hiram)", correct: false},
                    { text: "Uri", correct: false}
                ]
            },
            {
                question: "Which of the Minor Prophets delivered an oracle consisting of a single chapter, primarily focused on the condemnation of Edom?",
                answers: [
                    { text: "Nahum", correct: false},
                    { text: "Habakkuk", correct: false},
                    { text: "Obadiah", correct: true},
                    { text: "Zephaniah", correct: false}
                ]
            },
            {
                question: "According to the Book of Hebrews, Jesus Christ is a high priest forever in the order of whom, predating the Aaronic priesthood?",
                answers: [
                    { text: "Aaron", correct: false},
                    { text: "Levi", correct: false},
                    { text: "Zadok", correct: false},
                    { text: "Melchizedek", correct: true}
                ]
            },
            {
                question: "What was the name of the Aegean island where the Apostle John was exiled and received the visions recorded in the Book of Revelation?",
                answers: [
                    { text: "Crete", correct: false},
                    { text: "Cyprus", correct: false},
                    { text: "Patmos", correct: true},
                    { text: "Malta", correct: false}
                ]
            },
            {
                question: "In Paul's personal letter to Philemon, what was the name of the runaway slave whom Paul urged Philemon to receive back as a brother in Christ?",
                answers: [
                    { text: "Tychicus", correct: false},
                    { text: "Onesimus", correct: true},
                    { text: "Epaphras", correct: false},
                    { text: "Archippus", correct: false}
                ]
            },
            {
                question: "Which of the twelve tribes of Israel was set apart for priestly duties and therefore did not receive a territorial inheritance in the Promised Land like the other tribes?",
                answers: [
                    { text: "Judah", correct: false},
                    { text: "Benjamin", correct: false},
                    { text: "Levi", correct: true},
                    { text: "Joseph (Ephraim/Manasseh)", correct: false}
                ]
            },
            {
                question: "What was the name of Moses' Midianite father-in-law, also known as Reuel, who gave Moses wise counsel on delegating judicial responsibilities?",
                answers: [
                    { text: "Hobab", correct: false},
                    { text: "Gershom", correct: false},
                    { text: "Eliezer", correct: false},
                    { text: "Jethro", correct: true}
                ]
            },
            {
                question: "Which king of Judah was struck with leprosy (tzaraath) by God for presumptuously entering the temple to burn incense on the altar, a duty reserved for priests?",
                answers: [
                    { text: "Hezekiah", correct: false},
                    { text: "Manasseh", correct: false},
                    { text: "Uzziah (Azariah)", correct: true},
                    { text: "Josiah", correct: false}
                ]
            },
            {
                question: "The prominent 'Suffering Servant' songs, which prophetically detail the vicarious suffering and exaltation of the Messiah, are found primarily in which Old Testament book?",
                answers: [
                    { text: "Jeremiah", correct: false},
                    { text: "Isaiah", correct: true},
                    { text: "Ezekiel", correct: false},
                    { text: "Daniel", correct: false}
                ]
            },
            {
                question: "What was the original Hebrew name of the tax collector whom Jesus called to be an apostle, also known by his Greek name Matthew?",
                answers: [
                    { text: "Simon", correct: false},
                    { text: "Levi", correct: true},
                    { text: "Zacchaeus", correct: false},
                    { text: "Nathanael", correct: false}
                ]
            },
            {
                question: "In the Sermon on the Mount (Matthew 5-7), Jesus presents a series of 'antitheses' beginning with 'You have heard that it was said... But I say to you...' How many distinct antitheses are traditionally identified in Matthew chapter 5?",
                answers: [
                    { text: "Four", correct: false},
                    { text: "Five", correct: false},
                    { text: "Six", correct: true},
                    { text: "Seven", correct: false}
                ]
            },
            {
                question: "Which prophet, initially reluctant to obey God's call, saw a vision of a valley of dry bones miraculously coming to life, symbolizing God's power to restore Israel?",
                answers: [
                    { text: "Isaiah", correct: false},
                    { text: "Jeremiah", correct: false},
                    { text: "Daniel", correct: false},
                    { text: "Ezekiel", correct: true}
                ]
            },
            {
                question: "To the church in which major city did Paul write his longest and most systematically theological epistle, outlining core doctrines such as justification by faith?",
                answers: [
                    { text: "Corinth", correct: false},
                    { text: "Ephesus", correct: false},
                    { text: "Rome", correct: true},
                    { text: "Galatia", correct: false}
                ]
            }
        ];
        const questionElement = document.getElementById("question");
        const answerButton = document.getElementById("answer-buttons");
        const nextButton = document.getElementById("next-btn");

        let currentQuestionIndex = 0;
        let score = 0;

        function startQuiz(){
            currentQuestionIndex = 0;
            score = 0;
            nextButton.innerHTML = "Next";
            showQuestion();
        }

        function showQuestion(){
            resetState();
            let currentQuestion = questions[currentQuestionIndex];
            let questionNo = currentQuestionIndex + 1;
            questionElement.innerHTML = questionNo + ". " + currentQuestion.question;

            currentQuestion.answers.forEach(answer => {
                const button = document.createElement("button");
                button.innerHTML = answer.text;
                button.classList.add("btnQ");
                answerButton.appendChild(button);
                if(answer.correct){
                    button.dataset.correct = answer.correct;
                }
                button.addEventListener("click", selectAnswer);
            });
        }

        function resetState(){
            nextButton.style.display = "none";
            while(answerButton.firstChild){
                answerButton.removeChild(answerButton.firstChild);
            }
        }

        function selectAnswer(e){
            const selectBtn = e.target;
            const isCorrect = selectBtn.dataset.correct === "true";
            if(isCorrect){
                selectBtn.classList.add("correct");
                score++;
            }else{
                selectBtn.classList.add("incorrect");
            }
            Array.from(answerButton.children).forEach(button => {
                if(button.dataset.correct === "true"){
                    button.classList.add("correct");
                }
                button.disabled = true;
            });
            nextButton.style.display = "block";
        }

        function showScore(){
            resetState();
            questionElement.innerHTML = `You Scored ${score} out of ${questions.length}!`;
            nextButton.innerHTML = "Play Again";
            nextButton.style.display = "block";
        }

        function handleNextButton(){
            currentQuestionIndex++;
            if(currentQuestionIndex < questions.length){
                showQuestion();
            }else{
                showScore();
            }
        }

        nextButton.addEventListener("click", ()=>{
            if(currentQuestionIndex < questions.length){
                handleNextButton();
            }else{
                startQuiz();
            }
        });

        startQuiz();
    </script>
</body>
</html>