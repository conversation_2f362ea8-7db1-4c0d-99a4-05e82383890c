<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        .app{
            background-image: url(https://bakibra.dev/wp-content/uploads/2024/03/BB_Pattern_white.png);
            width: 90%;
            max-width: 600px;
            margin: 100px auto 0;
            border-radius: 10px;
            padding: 30px;
        }
        .h1Q{
            font-size: 25px;
            color:#0C0D0D;
            font-weight: 600;
            border-bottom: 1px solid #333;
            padding-bottom: 30px;
            text-align: center;
        }
        .quiz{
            padding: 20px 0;
        }
        .quiz .h2Q{
            font-size: 20px;
            color: #0C0D0D;
            font-weight: 600;
        }
        .btnQ{
            background: #F5923F;
            color: #FFFFFF;
            font-weight: 500;
            width: 100%;
            border: 1px solid #0C0D0D;
            padding: 10px;
            margin: 10px 0;
            text-align: left;
            border-radius: 4px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btnQ:hover:not([disabled]){
            background: #2A1148;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 500;
        }
        .btnQ:disabled{
            cursor: no-drop;
        }
        #next-btn{
            background: #F5923F;
            color: #FFFFFF;
            font-size: 18px;
            font-weight: 500;
            width: 150px;
            border: 1px solid #0C0D0D;
            padding: 10px;
            margin: 20px auto 0;
            border-radius: 4px;
            cursor: pointer;
            display: none;
        }
        .correct{
            background: #9aeabc;
            color: #0C0D0D;
        }
        .incorrect{
            background: #ff9393;
            color: #0C0D0D;
        }
    </style>
</head>
<body>
    <div class="app">
        <h1 class="h1Q">Intermediate Bible Scholars Quiz</h1>
        <div class="quiz">
            <h2   class="h2Q"id="question">Question goes here</h2>
            <div id="answer-buttons">
                <button class="btnQ">Answer 1</button>
                <button class="btnQ">Answer 2</button>
                <button class="btnQ">Answer 3</button>
                <button class="btnQ">Answer 4</button>
            </div>
            <button id="next-btn">Next</button>
        </div>
    </div>
    <script>
        const questions = [
            {
                question: "Who was Ishbosheth in relation to King Saul?",
                answers: [
                    { text: "His armor-bearer", correct: false},
                    { text: "His son who briefly reigned after him", correct: true},
                    { text: "His most trusted general", correct: false},
                    { text: "His court prophet", correct: false}
                ]
            },
            {
                question: "Which prophet famously confronted King David with a parable regarding his sin with Bathsheba and Uriah?",
                answers: [
                    { text: "Elijah", correct: false},
                    { text: "Samuel", correct: false},
                    { text: "Nathan", correct: true},
                    { text: "Isaiah", correct: false}
                ]
            },
            {
                question: "The 'Valley of Achor,' a place of trouble due to Achan's sin, was promised to become a 'door of hope' by which prophet?",
                answers: [
                    { text: "Jeremiah", correct: false},
                    { text: "Ezekiel", correct: false},
                    { text: "Isaiah", correct: false},
                    { text: "Hosea", correct: true}
                ]
            },
            {
                question: "What was the name of the silversmith in Ephesus who incited a riot against Paul because the spread of the Gospel was harming the trade of Artemis idols?",
                answers: [
                    { text: "Alexander", correct: false},
                    { text: "Demetrius", correct: true},
                    { text: "Gaius", correct: false},
                    { text: "Sceva", correct: false}
                ]
            },
            {
                question: "In Paul's letter to the Galatians, he mentions meeting with the 'pillars' of the Jerusalem church. Besides James, who were the other two apostles he named?",
                answers: [
                    { text: "Andrew and Philip", correct: false},
                    { text: "Thomas and Bartholomew", correct: false},
                    { text: "Peter (Cephas) and John", correct: true},
                    { text: "Matthew and Thaddaeus", correct: false}
                ]
            },
            {
                question: "According to the Book of Numbers, what specific consequence did Moses and Aaron face for striking the rock at Meribah instead of speaking to it as God commanded?",
                answers: [
                    { text: "They were immediately removed from leadership roles.", correct: false},
                    { text: "They were not permitted to enter the Promised Land.", correct: true},
                    { text: "Their priestly duties were temporarily suspended.", correct: false},
                    { text: "They had to offer an additional, special sin offering.", correct: false}
                ]
            },
            {
                question: "Which Old Testament judge was left-handed and used this characteristic to assassinate Eglon, the portly king of Moab?",
                answers: [
                    { text: "Othniel", correct: false},
                    { text: "Shamgar", correct: false},
                    { text: "Gideon", correct: false},
                    { text: "Ehud", correct: true}
                ]
            },
            {
                question: "The book of Lamentations poignantly mourns which major catastrophic event in Israel's history?",
                answers: [
                    { text: "The Assyrian captivity of the northern kingdom of Israel.", correct: false},
                    { text: "The destruction of Jerusalem and Solomon's Temple by the Babylonians.", correct: true},
                    { text: "The death of the righteous King Josiah in battle.", correct: false},
                    { text: "The great famine during the time of the prophet Elijah.", correct: false}
                ]
            },
            {
                question: "In the New Testament city of Philippi, who is described as a 'seller of purple goods' from Thyatira, whose heart the Lord opened to Paul's message?",
                answers: [
                    { text: "Priscilla", correct: false},
                    { text: "Phoebe", correct: false},
                    { text: "Lydia", correct: true},
                    { text: "Dorcas (Tabitha)", correct: false}
                ]
            },
            {
                question: "Which Minor Prophet's book is almost entirely dedicated to a prophecy of doom against the Assyrian city of Nineveh, delivered long after Jonah's mission?",
                answers: [
                    { text: "Obadiah", correct: false},
                    { text: "Micah", correct: false},
                    { text: "Nahum", correct: true},
                    { text: "Habakkuk", correct: false}
                ]
            },
            {
                question: "What item of clothing, belonging to Elijah, did Elisha pick up from the ground after Elijah was taken to heaven, symbolizing the transfer of prophetic power?",
                answers: [
                    { text: "His sandals", correct: false},
                    { text: "His staff", correct: false},
                    { text: "His belt", correct: false},
                    { text: "His mantle (cloak)", correct: true}
                ]
            },
            {
                question: "In Jesus' Parable of the Sower, what does the seed that fell among thorns represent?",
                answers: [
                    { text: "Those who hear the word, but Satan immediately snatches it away.", correct: false},
                    { text: "Those who receive the word with joy but fall away when persecution comes.", correct: false},
                    { text: "Those who hear the word, but the worries of life and the deceitfulness of wealth choke it.", correct: true},
                    { text: "Those who hear the word, understand it, and produce a plentiful crop.", correct: false}
                ]
            },
            {
                question: "Of the seven churches in Asia Minor addressed in Revelation, which one was located where 'Satan's throne' was and was commended for holding fast to Christ's name even when Antipas, His faithful witness, was killed?",
                answers: [
                    { text: "Ephesus", correct: false},
                    { text: "Smyrna", correct: false},
                    { text: "Thyatira", correct: false},
                    { text: "Pergamum", correct: true}
                ]
            },
            {
                question: "The 'Day of the LORD' is a significant eschatological theme. Which prophet vividly describes it using the imagery of a devastating locust plague?",
                answers: [
                    { text: "Amos", correct: false},
                    { text: "Joel", correct: true},
                    { text: "Zephaniah", correct: false},
                    { text: "Malachi", correct: false}
                ]
            },
            {
                question: "Who was the Roman centurion, a devout and God-fearing Gentile, to whom the Apostle Peter was divinely sent to share the Gospel, marking a pivotal moment for Gentile inclusion?",
                answers: [
                    { text: "Julius", correct: false},
                    { text: "Sergius Paulus", correct: false},
                    { text: "Cornelius", correct: true},
                    { text: "Claudius Lysias", correct: false}
                ]
            },
            {
                question: "Which of Jacob's sons, ancestor of a tribe known for its scribal and wisdom traditions, did not receive a territorial inheritance in Canaan like his brothers, but was instead given cities among the other tribes?",
                answers: [
                    { text: "Judah", correct: false},
                    { text: "Benjamin", correct: false},
                    { text: "Levi", correct: true},
                    { text: "Dan", correct: false}
                ]
            }
        ];
        const questionElement = document.getElementById("question");
        const answerButton = document.getElementById("answer-buttons");
        const nextButton = document.getElementById("next-btn");

        let currentQuestionIndex = 0;
        let score = 0;

        function startQuiz(){
            currentQuestionIndex = 0;
            score = 0;
            nextButton.innerHTML = "Next";
            showQuestion();
        }

        function showQuestion(){
            resetState();
            let currentQuestion = questions[currentQuestionIndex];
            let questionNo = currentQuestionIndex + 1;
            questionElement.innerHTML = questionNo + ". " + currentQuestion.question;

            currentQuestion.answers.forEach(answer => {
                const button = document.createElement("button");
                button.innerHTML = answer.text;
                button.classList.add("btnQ");
                answerButton.appendChild(button);
                if(answer.correct){
                    button.dataset.correct = answer.correct;
                }
                button.addEventListener("click", selectAnswer);
            });
        }

        function resetState(){
            nextButton.style.display = "none";
            while(answerButton.firstChild){
                answerButton.removeChild(answerButton.firstChild);
            }
        }

        function selectAnswer(e){
            const selectBtn = e.target;
            const isCorrect = selectBtn.dataset.correct === "true";
            if(isCorrect){
                selectBtn.classList.add("correct");
                score++;
            }else{
                selectBtn.classList.add("incorrect");
            }
            Array.from(answerButton.children).forEach(button => {
                if(button.dataset.correct === "true"){
                    button.classList.add("correct");
                }
                button.disabled = true;
            });
            nextButton.style.display = "block";
        }

        function showScore(){
            resetState();
            questionElement.innerHTML = `You Scored ${score} out of ${questions.length}!`;
            nextButton.innerHTML = "Play Again";
            nextButton.style.display = "block";
        }

        function handleNextButton(){
            currentQuestionIndex++;
            if(currentQuestionIndex < questions.length){
                showQuestion();
            }else{
                showScore();
            }
        }

        nextButton.addEventListener("click", ()=>{
            if(currentQuestionIndex < questions.length){
                handleNextButton();
            }else{
                startQuiz();
            }
        });

        startQuiz();
    </script>
</body>
</html>